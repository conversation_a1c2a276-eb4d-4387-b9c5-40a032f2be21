import { ShipperType } from './services/interfaces/order.interface';
import { EnumPayFailedType } from './services/interfaces/repay.interface';

export const CLINICO = 'CLINICO';
export const IB = 'IB';
export const SKD = 'SKD';

/*
科林督導
1	8	北區-台北一區	L1069[蔡秉忻]	<<EMAIL>>
1	9	北區-台北二區	L1306[張毓尹]	<<EMAIL>>
1	10	北區-台北三區	L1134[謝旻諺]	<<EMAIL>>
1	11	北區-新北一區	L1117[王孝勳]	<<EMAIL>>
1	12	北區-新北二區	L1375[包育誠]	<<EMAIL>>
1	13	北區-新北三區	L0715[林宜萱]	<<EMAIL>>
1	14	北區-桃竹一區	L1172[宋長志]	<<EMAIL>>
2	15	中區-中彰一區	L0431[賴敬文]	<laichi<PERSON><EMAIL>>
2	16	中區-中彰二區	L1028[陳佳聖]	<<EMAIL>>
3	17	南區-雲嘉南區	L0518[謝維靜]	<<EMAIL>>
3	18	南區-台南一區	L0546[陳雅惠]	<<EMAIL>>
3	19	南區-高屏一區	L0332[蕭璇涵]	<<EMAIL>>

濰樂督導
21	-	北區	L0725[劉穎奇]	<<EMAIL>>
25	-	中區	L0861[劉幸旻]	<<EMAIL>>
26	-	南區	L1566[楊欣樺]	<<EMAIL>>

聽寶督導
23	-	北東區	L1230[丁薏菁]	<<EMAIL>>
24	-	中南區	L1230[丁薏菁]	<<EMAIL>>
*/
export const managers = [
    {
        zone1: 1,
        zone2: 8,
        zoneName: '台北一區',
        name: '蔡秉忻',
        email: '<EMAIL>',
        code: 'L1069',
    },
    {
        zone1: 1,
        zone2: 9,
        zoneName: '台北二區',
        name: '張毓尹',
        email: '<EMAIL>',
        code: 'L1306',
    },
    {
        zone1: 1,
        zone2: 10,
        zoneName: '台北三區',
        name: '謝旻諺',
        email: '<EMAIL>',
        code: 'L1134',
    },
    {
        zone1: 1,
        zone2: 11,
        zoneName: '新北一區',
        name: '王孝勳',
        email: '<EMAIL>',
        code: 'L1117',
    },
    {
        zone1: 1,
        zone2: 12,
        zoneName: '新北二區',
        name: '包育誠',
        email: '<EMAIL>',
        code: 'L1375',
    },
    {
        zone1: 1,
        zone2: 13,
        zoneName: '新北三區',
        name: '蔡秉忻',
        email: '<EMAIL>',
        code: 'L1069',
    },
    {
        zone1: 1,
        zone2: 14,
        zoneName: '桃竹一區',
        name: '宋長志',
        email: '<EMAIL>',
        code: 'L1172',
    },
    {
        zone1: 2,
        zone2: 15,
        zoneName: '中彰一區',
        name: '賴敬文',
        email: '<EMAIL>',
        code: 'L0431',
    },
    {
        zone1: 2,
        zone2: 16,
        zoneName: '中彰二區',
        name: '陳佳聖',
        email: '<EMAIL>',
        code: 'L1028',
    },
    {
        zone1: 3,
        zone2: 17,
        zoneName: '雲嘉南區',
        name: '謝維靜',
        email: '<EMAIL>',
        code: 'L0518',
    },
    {
        zone1: 3,
        zone2: 18,
        zoneName: '台南一區',
        name: '謝昀庭',
        email: '<EMAIL>',
        code: 'L2008',
    },
    {
        zone1: 3,
        zone2: 19,
        zoneName: '高屏一區',
        name: '蕭璇涵',
        email: '<EMAIL>',
        code: 'L0332',
    },
    {
        zone1: 21,
        zone2: null,
        zoneName: '北區',
        name: '劉穎奇',
        email: '<EMAIL>',
        code: 'L0725',
    },
    {
        zone1: 25,
        zone2: null,
        zoneName: '中區',
        name: '劉幸旻',
        email: '<EMAIL>',
        code: 'L0861',
    },
    {
        zone1: 26,
        zone2: null,
        zoneName: '南區',
        name: '楊欣樺',
        email: '<EMAIL>',
        code: 'L1566',
    },
    {
        zone1: 23,
        zone2: null,
        zoneName: '北東區',
        name: '丁薏菁',
        email: '<EMAIL>',
        code: 'L1230',
    },
    {
        zone1: 24,
        zone2: null,
        zoneName: '中南區',
        name: '丁薏菁',
        email: '<EMAIL>',
        code: 'L1230',
    },
];

// 銀行存款不足/簽帳金融卡餘額不足 代碼
export const insufficientAmountCodes = ['01', '02', '51'];

/* 各期數交易失敗簡訊內容 */
export const payFailedMessageForSMS: {
    [key in EnumPayFailedType]: {
        errorCode?: string[];
        reason?: string;
        orderType: ShipperType[] | string[];
        template: string;
    }[];
} = {
    Normal: [
        {
            errorCode: insufficientAmountCodes,
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，本期{{orderType}}訂閱因帳戶餘額不足扣款失敗，請補足{{amount}}元至帳戶，{{retryDate}}將再次扣款，謝謝。客服{{serviceNumber}}',
        },
        {
            errorCode: insufficientAmountCodes,
            orderType: [ShipperType.FOOD],
            template:
                '【{{companyName}}通知】親愛的用戶，本期保健食品訂閱因帳戶餘額不足扣款失敗，請補足{{amount}}元至帳戶，{{retryDate}}將再次扣款，補款後我們將安排出貨，謝謝。客服專線{{serviceNumber}}',
        },
        {
            reason: '其他原因',
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，本期{{orderType}}訂閱因{{method}}問題扣款未成功，請聯繫門市處理，確保服務不受影響，謝謝。聯繫門市：{{storeName}}{{storePhone}}',
        },
        {
            reason: '其他原因',
            orderType: [ShipperType.FOOD],
            template:
                '【{{companyName}}通知】親愛的用戶，本期保健食品訂閱因{{method}}問題扣款失敗，請聯繫{{storeName}}{{storePhone}}。補款後我們將安排出貨，謝謝。',
        },
        {
            reason: '補刷失敗',
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，本次訂閱服務補繳款未成功，請聯繫客服專線{{serviceNumber}}，如已繳款請忽略此封簡訊，謝謝。',
        },
        {
            reason: '補刷失敗',
            orderType: [ShipperType.FOOD],
            template:
                '【{{companyName}}通知】親愛的用戶，本次訂閱服務補繳款未成功，請聯繫客服專線{{serviceNumber}}，如已繳款請忽略此封簡訊，謝謝。',
        },
    ],
    ConsecutivePeriods2: [
        {
            errorCode: insufficientAmountCodes,
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template: `【{{companyName}}通知】親愛的用戶，提醒您{{orderType}}訂閱服務，因帳戶餘額不足，而累計2期扣款失敗，請補足{{totalAmount}}元至帳戶，{{retryDate}}將再次扣款，謝謝。客服{{serviceNumber}}`,
        },
        {
            reason: '其他原因',
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，提醒您{{orderType}}訂閱服務，因{{method}}問題，而累計2期扣款失敗，請聯繫門市處理，確保服務不受影響，謝謝。聯繫門市：{{storeName}}{{storePhone}}',
        },
        {
            reason: '其他原因',
            orderType: [ShipperType.FOOD],
            template:
                '【{{companyName}}通知】親愛的用戶，本期保健食品訂閱因{{method}}問題，累計2期扣款失敗，如需暫停訂閱請聯繫門市，謝謝。聯繫門市：{{storeName}}{{storePhone}}',
        },
        {
            reason: '補刷失敗',
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，本次訂閱服務補繳款未成功，請聯繫客服專線{{serviceNumber}}，如已繳款請忽略此封簡訊，謝謝。',
        },
    ],
    ConsecutivePeriods3: [
        {
            reason: '不分原因',
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，您{{orderType}}訂閱已累計3期未繳納，共{{totalAmount}}元，如需取消訂閱請聯繫{{storeName}}{{storePhone}}，其他服務請洽客服{{serviceNumber}}，謝謝。',
        },
        {
            reason: '補刷失敗',
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，本次訂閱服務補繳款未成功，請聯繫客服專線{{serviceNumber}}，如已繳款請忽略此封簡訊，謝謝。',
        },
    ],
    ConsecutivePeriods4: [
        {
            reason: '不分原因',
            orderType: [ShipperType.HA, ShipperType.RS],
            template:
                '【{{companyName}}通知】親愛的用戶，您{{orderType}}訂閱已累計4期未繳，共{{totalAmount}}元，我們將依合約中止服務，請聯繫門市歸還設備，{{storeName}}{{storePhone}}，其他服務請洽客服{{serviceNumber}}，謝謝。',
        },
        {
            reason: '補刷失敗',
            orderType: [ShipperType.HA, ShipperType.RS, ShipperType.RSM],
            template:
                '【{{companyName}}通知】親愛的用戶，本次訂閱服務補繳款未成功，請聯繫客服專線{{serviceNumber}}，如已繳款請忽略此封簡訊，謝謝。',
        },
    ],
};
